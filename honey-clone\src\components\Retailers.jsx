const Retailers = () => {
  const retailers = [
    { name: 'Amazon', logo: '🛒' },
    { name: 'Target', logo: '🎯' },
    { name: 'Walmart', logo: '🏪' },
    { name: 'Best Buy', logo: '💻' },
    { name: 'Nike', logo: '👟' },
    { name: 'Adidas', logo: '👕' },
    { name: 'Sephora', logo: '💄' },
    { name: 'Home Depot', logo: '🔨' },
    { name: 'Macy\'s', logo: '👗' },
    { name: 'eBay', logo: '🏷️' }
  ]

  return (
    <section className="retailers">
      <div className="container">
        <div className="retailers-content">
          <h2 className="section-title">30,000+ stores</h2>
          <p className="section-subtitle">
            Whether you're ordering pizza, picking up shoes, or booking travel, 
            we'll look for coupons when you shop on thousands of sites.
          </p>
          
          <div className="retailers-grid">
            {retailers.map((retailer, index) => (
              <div key={index} className="retailer-item">
                <div className="retailer-logo">
                  <span className="logo-emoji">{retailer.logo}</span>
                </div>
                <span className="retailer-name">{retailer.name}</span>
              </div>
            ))}
          </div>

          <div className="cta-section">
            <h3 className="cta-title">Too good to pass up, right? Start saving now.</h3>
            <a href="#" className="btn btn-primary btn-large">
              Add to Chrome
            </a>
            <p className="browser-support">
              Currently, we only support Chrome, Safari, Firefox, Edge, and Opera.
            </p>
          </div>
        </div>
      </div>

      <style jsx>{`
        .retailers {
          background: #ecf7f8;
          padding: 120px 0;
          text-align: center;
          width: 100%;
        }

        .retailers-content {
          max-width: 800px;
          margin: 0 auto;
          padding: 0 20px;
        }

        .section-title {
          font-size: 48px;
          font-weight: 500;
          color: #195154;
          margin-bottom: 16px;
          line-height: 1.2;
        }

        @media (max-width: 768px) {
          .section-title {
            font-size: 36px;
          }
        }

        .section-subtitle {
          font-size: 18px;
          line-height: 1.6;
          color: #195154;
          margin-bottom: 60px;
          max-width: 620px;
          margin-left: auto;
          margin-right: auto;
        }

        @media (max-width: 768px) {
          .section-subtitle {
            font-size: 16px;
            margin-bottom: 40px;
          }
        }

        .retailers-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 24px;
          margin-bottom: 80px;
          max-width: 612px;
          margin-left: auto;
          margin-right: auto;
        }

        @media (min-width: 414px) {
          .retailers-grid {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        @media (min-width: 768px) {
          .retailers-grid {
            grid-template-columns: repeat(5, 1fr);
          }
        }

        .retailer-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 20px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: transform 0.2s, box-shadow 0.2s;
          cursor: pointer;
        }

        .retailer-item:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .retailer-logo {
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f8f9fa;
          border-radius: 50%;
          margin-bottom: 12px;
        }

        .logo-emoji {
          font-size: 24px;
        }

        .retailer-name {
          font-size: 14px;
          font-weight: 500;
          color: #212121;
          text-align: center;
        }

        .cta-section {
          padding-top: 40px;
          border-top: 1px solid rgba(25, 81, 84, 0.1);
        }

        .cta-title {
          font-size: 32px;
          font-weight: 500;
          color: #195154;
          margin-bottom: 32px;
          line-height: 1.3;
        }

        @media (max-width: 768px) {
          .cta-title {
            font-size: 24px;
            margin-bottom: 24px;
          }
        }

        .browser-support {
          font-size: 12px;
          color: #757575;
          margin-top: 12px;
          max-width: 400px;
          margin-left: auto;
          margin-right: auto;
        }
      `}</style>
    </section>
  )
}

export default Retailers
