const Features = () => {
  const features = [
    {
      id: 'extension',
      title: 'THE EXTENSION',
      subtitle: 'Automatic coupons',
      description: "Still looking for codes on your own? We'll search for you. With a single click, we'll test codes at checkout to help you find a deal.",
      bgColor: '#f5f5f5',
      textColor: '#2743a0'
    },
    {
      id: 'droplist',
      title: 'THE PRICE TRACKER',
      subtitle: 'Droplist',
      description: "Have an item you're not ready to buy? Add it to Droplist. If we detect a price drop at that store, we'll notify you.",
      bgColor: '#f5f5f5',
      textColor: '#237176'
    },
    {
      id: 'amazon',
      title: 'HONEY ON AMAZON',
      subtitle: 'Want better Amazon prices?',
      description: "We compare sellers for you — even factoring in shipping and Prime status. Throw in price tracking and price history tools for more chances to save.",
      bgColor: '#f5f5f5',
      textColor: '#7a47e7'
    }
  ]

  return (
    <section className="features">
      {features.map((feature, index) => (
        <div key={feature.id} className={`feature-section ${feature.id}`}>
          <div className="container">
            <div className="feature-content">
              <div className="feature-text">
                <div className="feature-label" style={{ color: feature.textColor }}>
                  {feature.title}
                </div>
                <h2 className="feature-title">{feature.subtitle}</h2>
                <p className="feature-description">{feature.description}</p>
                {feature.id === 'amazon' && (
                  <p className="amazon-disclaimer">
                    Note: Amazon and Honey are not affiliated.
                  </p>
                )}
              </div>
              
              <div className="feature-visual">
                <div className="feature-mockup">
                  {feature.id === 'extension' && (
                    <div className="extension-demo">
                      <div className="coupon-popup">
                        <div className="popup-header">
                          <span className="honey-icon">🍯</span>
                          <span>Honey found 2 coupons</span>
                        </div>
                        <div className="coupon-item">
                          <span className="coupon-code">SAVE20</span>
                          <span className="coupon-savings">Save $12.99</span>
                        </div>
                        <button className="apply-button">Apply</button>
                      </div>
                    </div>
                  )}
                  
                  {feature.id === 'droplist' && (
                    <div className="droplist-demo">
                      <div className="product-card">
                        <div className="product-image"></div>
                        <div className="product-info">
                          <h4>Wireless Headphones</h4>
                          <div className="price-info">
                            <span className="old-price">$199.99</span>
                            <span className="new-price">$149.99</span>
                            <span className="price-drop">25% off</span>
                          </div>
                        </div>
                        <div className="droplist-notification">
                          Price dropped! 🎉
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {feature.id === 'amazon' && (
                    <div className="amazon-demo">
                      <div className="amazon-comparison">
                        <div className="seller-option">
                          <span className="seller-name">Amazon</span>
                          <span className="seller-price">$89.99</span>
                          <span className="shipping">+ $5.99 shipping</span>
                        </div>
                        <div className="seller-option best">
                          <span className="seller-name">Best Deal</span>
                          <span className="seller-price">$79.99</span>
                          <span className="shipping">Free shipping</span>
                          <span className="badge">Best Value</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}

      <style jsx>{`
        .features {
          background: white;
          width: 100%;
        }

        .feature-section {
          padding: 80px 0;
          background: #f5f5f5;
          width: 100%;
        }

        .feature-section:nth-child(even) {
          background: white;
        }

        .feature-content {
          display: flex;
          align-items: center;
          gap: 80px;
          min-height: 500px;
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
        }

        @media (max-width: 768px) {
          .feature-content {
            flex-direction: column;
            gap: 40px;
            text-align: center;
          }
        }

        .feature-text {
          flex: 1;
          max-width: 400px;
        }

        .feature-label {
          font-size: 12px;
          font-weight: 600;
          letter-spacing: 1px;
          text-transform: uppercase;
          margin-bottom: 8px;
        }

        .feature-title {
          font-size: 36px;
          font-weight: 500;
          color: #212121;
          margin-bottom: 24px;
          line-height: 1.2;
        }

        @media (max-width: 768px) {
          .feature-title {
            font-size: 28px;
          }
        }

        .feature-description {
          font-size: 18px;
          line-height: 1.6;
          color: #616161;
          margin-bottom: 16px;
        }

        .amazon-disclaimer {
          font-size: 11px;
          font-style: italic;
          color: #757575;
          margin-top: 20px;
        }

        .feature-visual {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .feature-mockup {
          width: 400px;
          height: 300px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        @media (max-width: 768px) {
          .feature-mockup {
            width: 100%;
            max-width: 350px;
          }
        }

        /* Extension Demo Styles */
        .extension-demo {
          width: 100%;
          display: flex;
          justify-content: center;
        }

        .coupon-popup {
          background: white;
          border: 2px solid #ffc74f;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
          min-width: 280px;
        }

        .popup-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 16px;
          font-weight: 600;
          color: #212121;
        }

        .honey-icon {
          font-size: 18px;
        }

        .coupon-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px;
          background: #f9f9f9;
          border-radius: 4px;
          margin-bottom: 12px;
        }

        .coupon-code {
          font-family: monospace;
          font-weight: bold;
          color: #212121;
        }

        .coupon-savings {
          color: #cc4b06;
          font-weight: 600;
        }

        .apply-button {
          width: 100%;
          padding: 12px;
          background: #cc4b06;
          color: white;
          border: none;
          border-radius: 4px;
          font-weight: 600;
          cursor: pointer;
        }

        /* Droplist Demo Styles */
        .droplist-demo {
          width: 100%;
          display: flex;
          justify-content: center;
        }

        .product-card {
          background: white;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          position: relative;
          min-width: 280px;
        }

        .product-image {
          width: 100%;
          height: 120px;
          background: #f0f0f0;
          border-radius: 4px;
          margin-bottom: 12px;
        }

        .product-info h4 {
          margin-bottom: 8px;
          color: #212121;
        }

        .price-info {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .old-price {
          text-decoration: line-through;
          color: #999;
        }

        .new-price {
          font-weight: bold;
          color: #cc4b06;
        }

        .price-drop {
          background: #e8f5e8;
          color: #2e7d32;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 12px;
        }

        .droplist-notification {
          position: absolute;
          top: -10px;
          right: -10px;
          background: #4caf50;
          color: white;
          padding: 6px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 600;
        }

        /* Amazon Demo Styles */
        .amazon-demo {
          width: 100%;
          display: flex;
          justify-content: center;
        }

        .amazon-comparison {
          background: white;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          min-width: 280px;
        }

        .seller-option {
          display: flex;
          flex-direction: column;
          padding: 16px;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          margin-bottom: 12px;
          position: relative;
        }

        .seller-option.best {
          border-color: #7a47e7;
          background: #f8f6ff;
        }

        .seller-name {
          font-weight: 600;
          color: #212121;
          margin-bottom: 4px;
        }

        .seller-price {
          font-size: 18px;
          font-weight: bold;
          color: #cc4b06;
          margin-bottom: 2px;
        }

        .shipping {
          font-size: 12px;
          color: #666;
        }

        .badge {
          position: absolute;
          top: -8px;
          right: 8px;
          background: #7a47e7;
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 10px;
          font-weight: 600;
        }
      `}</style>
    </section>
  )
}

export default Features
