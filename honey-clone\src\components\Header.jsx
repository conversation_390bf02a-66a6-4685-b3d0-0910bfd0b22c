import { useState } from 'react'
import { Menu, X } from 'lucide-react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="header">
      <div className="header-content">
        <div className="logo">
          <a href="/" className="logo-link">
            <img
              src="https://cdn.joinhoney.com/images/honey-logo.svg"
              alt="Honey"
              width="80"
              height="26"
              onError={(e) => {
                e.target.style.display = 'none'
                e.target.nextSibling.style.display = 'inline'
              }}
            />
            <span style={{display: 'none', color: '#f26c25', fontSize: '24px', fontWeight: 'bold'}}>
              🍯 Honey
            </span>
          </a>
        </div>

        <nav className={`nav ${isMenuOpen ? 'nav-open' : ''}`}>
          <ul className="nav-list">
            <li><a href="#features">Features</a></li>
            <li><a href="#stores">Stores</a></li>
            <li><a href="#help">Help</a></li>
          </ul>
        </nav>

        <div className="header-actions">
          <div className="auth-links">
            <span className="auth-link">Log in</span>
          </div>

          <button
            className="mobile-menu-toggle"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      <style jsx>{`
        .header {
          background: white;
          box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          z-index: 100;
          height: 64px;
          width: 100%;
        }

        .header-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 64px;
          max-width: 1360px;
          margin: 0 auto;
          padding: 0 16px;
        }

        .logo-link {
          display: flex;
          align-items: center;
          text-decoration: none;
          transition: opacity 0.2s;
        }

        .logo-link:hover {
          opacity: 0.9;
        }

        .nav {
          display: none;
        }

        @media (min-width: 1024px) {
          .nav {
            display: block;
            margin-left: 24px;
          }
        }

        .nav-list {
          display: flex;
          list-style: none;
          margin: 0;
          padding: 0;
        }

        .nav-list li {
          margin-right: 32px;
        }

        .nav-list li:last-child {
          margin-right: 0;
        }

        .nav-list a {
          color: #616161;
          text-decoration: none;
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          transition: color 0.2s;
        }

        .nav-list a:hover {
          color: #757575;
        }

        .header-actions {
          display: flex;
          align-items: center;
        }

        .auth-links {
          display: none;
        }

        @media (min-width: 1024px) {
          .auth-links {
            display: flex;
            margin-right: 24px;
          }
        }

        .auth-link {
          color: #000;
          font-size: 14px;
          font-weight: 500;
          text-decoration: underline;
          cursor: pointer;
          transition: color 0.2s;
        }

        .auth-link:hover {
          color: #212121;
        }

        .mobile-menu-toggle {
          display: flex;
          align-items: center;
          background: none;
          border: none;
          cursor: pointer;
          padding: 8px;
          margin-left: 16px;
        }

        @media (min-width: 1024px) {
          .mobile-menu-toggle {
            display: none;
          }
        }

        .nav-open {
          display: block;
          position: absolute;
          top: 64px;
          left: 0;
          right: 0;
          background: white;
          box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
          padding: 20px;
        }

        @media (min-width: 1024px) {
          .nav-open {
            position: static;
            box-shadow: none;
            padding: 0;
          }
        }

        .nav-open .nav-list {
          flex-direction: column;
        }

        .nav-open .nav-list li {
          margin-right: 0;
          margin-bottom: 16px;
        }

        @media (min-width: 1024px) {
          .nav-open .nav-list {
            flex-direction: row;
          }
          
          .nav-open .nav-list li {
            margin-right: 32px;
            margin-bottom: 0;
          }
        }
      `}</style>
    </header>
  )
}

export default Header
