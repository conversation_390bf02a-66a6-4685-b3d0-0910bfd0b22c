* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'SuisseIntl', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.43;
  color: #424242;
  background: #fafafa;
}

.App {
  min-height: 100vh;
}

.container {
  max-width: 1360px;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 768px) {
  .container {
    padding: 0 24px;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 40px;
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 40px;
  border: 1px solid;
  border-radius: 3px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.btn-primary {
  background-color: #cc4b06;
  border-color: #cc4b06;
  color: white;
}

.btn-primary:hover {
  background-color: #f26c25;
  border-color: #f26c25;
}

.btn-secondary {
  background-color: #3d68fb;
  border-color: #3d68fb;
  color: white;
}

.btn-secondary:hover {
  background-color: #6083fb;
  border-color: #6083fb;
}

.btn-large {
  height: 56px;
  padding: 0 24px;
  font-size: 17px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Add some spacing for fixed header */
body {
  padding-top: 0;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

/* Animation classes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Responsive text sizes */
@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }
}
