const HowItWorks = () => {
  const steps = [
    {
      number: "1",
      title: "Add in Seconds",
      description: "It just takes a few clicks to add to your computer and it's 100% free.",
      highlight: false
    },
    {
      number: "2", 
      title: "Shop Like Normal",
      description: "We'll automatically look for codes when you shop on select sites.",
      highlight: true
    },
    {
      number: "3",
      title: "We'll do the work",
      description: "With a single click, we'll test codes at checkout to help you find a deal.",
      highlight: false
    }
  ]

  return (
    <section className="how-it-works">
      <div className="container">
        <div className="content-wrapper">
          <div className="video-section">
            <div className="video-placeholder">
              <div className="play-button">
                <svg width="60" height="60" viewBox="0 0 60 60" fill="none">
                  <circle cx="30" cy="30" r="30" fill="rgba(255, 255, 255, 0.9)" />
                  <path d="M25 20L40 30L25 40V20Z" fill="#cc4b06" />
                </svg>
              </div>
              <div className="video-overlay">
                <span>Watch how Honey works</span>
              </div>
            </div>
          </div>

          <div className="steps-section">
            <h2 className="section-title">How it works</h2>
            
            <div className="steps-container">
              <div className="progress-bar">
                {steps.map((step, index) => (
                  <div 
                    key={index}
                    className={`progress-segment ${step.highlight ? 'active' : ''}`}
                  />
                ))}
              </div>

              <div className="steps-list">
                {steps.map((step, index) => (
                  <div 
                    key={index} 
                    className={`step ${step.highlight ? 'highlighted' : ''}`}
                  >
                    <div className="step-number">{step.number}</div>
                    <div className="step-content">
                      <h3 className="step-title">{step.title}</h3>
                      <p className="step-description">{step.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .how-it-works {
          background: white;
          padding: 80px 0;
          width: 100%;
        }

        .content-wrapper {
          display: flex;
          align-items: flex-start;
          gap: 100px;
          max-width: 1170px;
          margin: 0 auto;
          padding: 0 40px;
        }

        @media (max-width: 1065px) {
          .content-wrapper {
            flex-direction: column;
            gap: 40px;
          }
        }

        .video-section {
          flex: 1;
          display: flex;
          justify-content: flex-start;
          order: 1;
        }

        .video-placeholder {
          width: 500px;
          height: 350px;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border-radius: 12px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: relative;
          cursor: pointer;
          transition: transform 0.2s;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .video-placeholder:hover {
          transform: scale(1.02);
        }

        @media (max-width: 1065px) {
          .video-section {
            order: 2;
            justify-content: center;
          }
        }

        @media (max-width: 768px) {
          .video-placeholder {
            width: 100%;
            max-width: 500px;
            height: 300px;
          }
        }

        .play-button {
          margin-bottom: 20px;
          transition: transform 0.2s;
        }

        .video-placeholder:hover .play-button {
          transform: scale(1.1);
        }

        .video-overlay span {
          color: #424242;
          font-size: 16px;
          font-weight: 500;
        }

        .steps-section {
          flex: 1;
          max-width: 450px;
          order: 2;
          padding-top: 40px;
        }

        @media (max-width: 1065px) {
          .steps-section {
            max-width: 600px;
            text-align: center;
            order: 1;
            padding-top: 0;
          }
        }

        .section-title {
          font-size: 48px;
          font-weight: 500;
          color: #0f3133;
          margin-bottom: 60px;
          line-height: 1.2;
        }

        @media (max-width: 768px) {
          .section-title {
            font-size: 36px;
            margin-bottom: 40px;
          }
        }

        .steps-container {
          display: flex;
          gap: 32px;
        }

        @media (max-width: 1065px) {
          .steps-container {
            flex-direction: column;
            align-items: center;
          }
        }

        .progress-bar {
          display: flex;
          flex-direction: column;
          gap: 8px;
          width: 3px;
        }

        @media (max-width: 1065px) {
          .progress-bar {
            flex-direction: row;
            width: auto;
            height: 3px;
          }
        }

        .progress-segment {
          height: 93px;
          width: 1px;
          background: #bdbdbd;
          margin-left: 1px;
          transition: all 0.2s ease;
        }

        .progress-segment.active {
          width: 3px;
          background: #f26c25;
          margin-left: 0;
        }

        @media (max-width: 1065px) {
          .progress-segment {
            width: 285px;
            height: 3px;
          }
          
          .progress-segment.active {
            height: 3px;
          }
        }

        .steps-list {
          display: flex;
          flex-direction: column;
          gap: 44px;
        }

        @media (max-width: 1065px) {
          .steps-list {
            flex-direction: row;
            justify-content: space-around;
            width: 100%;
            max-width: 800px;
          }
        }

        @media (max-width: 768px) {
          .steps-list {
            flex-direction: column;
            gap: 32px;
          }
        }

        .step {
          color: #424242;
          transition: all 0.2s ease;
        }

        .step.highlighted {
          color: #757575;
        }

        .step-number {
          font-size: 48px;
          font-weight: 700;
          line-height: 1;
          margin-bottom: 8px;
        }

        .step-title {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 8px;
          color: inherit;
        }

        .step-description {
          font-size: 16px;
          line-height: 1.5;
          max-width: 260px;
          color: inherit;
        }

        @media (max-width: 1065px) {
          .step-description {
            max-width: 200px;
          }
        }

        @media (max-width: 768px) {
          .step-description {
            max-width: 300px;
          }
        }
      `}</style>
    </section>
  )
}

export default HowItWorks
