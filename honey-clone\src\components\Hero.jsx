import { Star } from 'lucide-react'

const Hero = () => {
  return (
    <section className="hero">
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              We search for coupons at 30,000+ sites to help you save money
            </h1>
            
            <div className="hero-cta">
              <a href="#" className="btn btn-primary btn-large">
                Add to Chrome
              </a>
              <p className="browser-support">
                Currently, we only support Chrome, Safari, Firefox, Edge, and Opera.
              </p>
            </div>

            <div className="reviews">
              <div className="stars">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} size={16} fill="#ffc74f" color="#ffc74f" />
                ))}
              </div>
              <div className="review-text">
                <span className="review-count">141,786</span> Chrome Store reviews
                <br />
                <span className="member-count">17 million</span> members and counting
              </div>
            </div>
          </div>

          <div className="hero-visual">
            <div className="browser-extension">
              <div className="browser-mockup">
                <div className="browser-header">
                  <div className="browser-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
                <div className="browser-content">
                  <div className="honey-popup">
                    <div className="popup-header">
                      <span className="honey-logo">🍯</span>
                      <span>Honey found 3 coupons</span>
                    </div>
                    <div className="coupon-savings">
                      <span className="savings-amount">Save $15.99</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .hero {
          background: linear-gradient(135deg, #c8e9eb 0%, #f0f8f9 100%);
          min-height: 692px;
          display: flex;
          align-items: center;
          padding-top: 64px;
          position: relative;
          overflow: hidden;
          width: 100%;
        }

        .hero-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          min-height: 600px;
          width: 100%;
          max-width: 1200px;
          margin: 0 auto;
        }

        @media (max-width: 768px) {
          .hero-content {
            flex-direction: column;
            text-align: center;
            min-height: 500px;
          }
        }

        .hero-text {
          flex: 1;
          max-width: 575px;
          padding-right: 40px;
        }

        @media (max-width: 768px) {
          .hero-text {
            padding-right: 0;
            margin-bottom: 40px;
          }
        }

        .hero-title {
          font-size: 64px;
          font-weight: 500;
          line-height: 72px;
          color: #195154;
          margin-bottom: 32px;
        }

        @media (max-width: 768px) {
          .hero-title {
            font-size: 50px;
            line-height: 56px;
          }
        }

        @media (max-width: 480px) {
          .hero-title {
            font-size: 36px;
            line-height: 44px;
          }
        }

        .hero-cta {
          margin-bottom: 32px;
        }

        .browser-support {
          font-size: 12px;
          color: #757575;
          margin-top: 12px;
          max-width: 300px;
        }

        .reviews {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          font-size: 12px;
          color: #424242;
        }

        .stars {
          display: flex;
          gap: 2px;
          margin-top: 2px;
        }

        .review-count,
        .member-count {
          font-weight: 700;
        }

        .hero-visual {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        @media (max-width: 768px) {
          .hero-visual {
            display: none;
          }
        }

        .browser-mockup {
          width: 400px;
          height: 300px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .browser-header {
          height: 40px;
          background: #f5f5f5;
          display: flex;
          align-items: center;
          padding: 0 16px;
        }

        .browser-dots {
          display: flex;
          gap: 8px;
        }

        .browser-dots span {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #ddd;
        }

        .browser-dots span:nth-child(1) {
          background: #ff5f57;
        }

        .browser-dots span:nth-child(2) {
          background: #ffbd2e;
        }

        .browser-dots span:nth-child(3) {
          background: #28ca42;
        }

        .browser-content {
          padding: 20px;
          height: 260px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .honey-popup {
          background: #fff4dc;
          border: 2px solid #ffc74f;
          border-radius: 8px;
          padding: 20px;
          text-align: center;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .popup-header {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          margin-bottom: 12px;
          font-weight: 600;
          color: #212121;
        }

        .honey-logo {
          font-size: 20px;
        }

        .savings-amount {
          font-size: 24px;
          font-weight: 700;
          color: #cc4b06;
        }
      `}</style>
    </section>
  )
}

export default Hero
