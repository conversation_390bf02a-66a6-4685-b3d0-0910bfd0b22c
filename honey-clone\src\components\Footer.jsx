const Footer = () => {
  const footerSections = [
    {
      title: 'Company',
      links: [
        { name: 'About', href: '#' },
        { name: 'Help', href: '#' },
        { name: 'Careers', href: '#' },
        { name: 'News', href: '#' },
        { name: 'Blog', href: '#' },
        { name: 'Media', href: '#' }
      ]
    },
    {
      title: 'Product',
      links: [
        { name: 'Droplist', href: '#' },
        { name: 'Amazon Badge', href: '#' },
        { name: 'Reward<PERSON>', href: '#' }
      ]
    },
    {
      title: 'For Affiliates',
      links: [
        { name: 'Legal', href: '#' }
      ]
    },
    {
      title: 'Legal',
      links: [
        { name: 'Privacy', href: '#' },
        { name: 'Copyright', href: '#' },
        { name: 'Patents', href: '#' },
        { name: 'Terms', href: '#' },
        { name: 'Ad Disclosure', href: '#' },
        { name: 'E-Communication', href: '#' },
        { name: 'Accessibility', href: '#' },
        { name: 'Cook<PERSON>', href: '#' }
      ]
    }
  ]

  const socialLinks = [
    { name: 'Facebook', href: '#', icon: '📘' },
    { name: 'Twitter', href: '#', icon: '🐦' },
    { name: 'Instagram', href: '#', icon: '📷' },
    { name: 'Pinterest', href: '#', icon: '📌' }
  ]

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-links">
            <div className="links-grid">
              {footerSections.map((section, index) => (
                <div key={index} className="footer-section">
                  <h3 className="section-title">{section.title}</h3>
                  <ul className="section-links">
                    {section.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <a href={link.href} className="footer-link">
                          {link.name}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          <div className="footer-right">
            <div className="social-section">
              <h3 className="section-title">Follow Us</h3>
              <div className="social-links">
                {socialLinks.map((social, index) => (
                  <a key={index} href={social.href} className="social-link">
                    <span className="social-icon">{social.icon}</span>
                    <span className="social-name">{social.name}</span>
                  </a>
                ))}
              </div>
            </div>

            <div className="app-section">
              <h3 className="section-title">Get the App</h3>
              <div className="app-links">
                <a href="#" className="app-link">
                  <div className="app-store-badge">
                    <span className="app-icon">📱</span>
                    <div className="app-text">
                      <span className="app-subtitle">Download on the</span>
                      <span className="app-title">App Store</span>
                    </div>
                  </div>
                </a>
                <a href="#" className="app-link">
                  <div className="app-store-badge">
                    <span className="app-icon">🤖</span>
                    <div className="app-text">
                      <span className="app-subtitle">Get it on</span>
                      <span className="app-title">Google Play</span>
                    </div>
                  </div>
                </a>
              </div>
              <p className="app-disclaimer">
                Google Play and the Google Play logo are trademarks of Google Inc.
                Apple and the Apple logo are trademarks of Apple Inc., registered in the U.S. and other countries.
              </p>
            </div>
          </div>
        </div>

        <div className="footer-bottom">
          <div className="paypal-info">
            <span className="honey-paypal">© 2025 PayPal, Inc.</span>
            <p className="paypal-text">
              Honey is now part of the PayPal family. 
              <a href="#" className="learn-more-link">Learn more here.</a>
            </p>
          </div>
          
          <div className="country-selector">
            <span className="country-text">United States</span>
            <span className="language-text">English</span>
          </div>
        </div>
      </div>

      <style jsx>{`
        .footer {
          background: #000;
          color: #fff;
          padding: 64px 0 32px;
        }

        .footer-content {
          display: flex;
          justify-content: space-between;
          margin-bottom: 64px;
        }

        @media (max-width: 768px) {
          .footer-content {
            flex-direction: column;
            gap: 40px;
          }
        }

        .footer-links {
          flex: 1;
        }

        .links-grid {
          display: flex;
          gap: 60px;
        }

        @media (max-width: 768px) {
          .links-grid {
            flex-wrap: wrap;
            gap: 40px;
          }
        }

        .footer-section {
          min-width: 120px;
        }

        .section-title {
          font-size: 16px;
          font-weight: 500;
          color: #fff;
          margin-bottom: 20px;
        }

        .section-links {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .section-links li {
          margin-bottom: 14px;
        }

        .footer-link {
          color: #757575;
          text-decoration: none;
          font-size: 14px;
          font-weight: 500;
          line-height: 26px;
          transition: color 0.2s;
        }

        .footer-link:hover {
          color: #9e9e9e;
        }

        .footer-right {
          display: flex;
          flex-direction: column;
          gap: 40px;
          min-width: 300px;
        }

        @media (max-width: 768px) {
          .footer-right {
            min-width: auto;
          }
        }

        .social-links {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .social-link {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #757575;
          text-decoration: none;
          font-size: 14px;
          transition: color 0.2s;
        }

        .social-link:hover {
          color: #9e9e9e;
        }

        .social-icon {
          font-size: 16px;
        }

        .app-links {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .app-link {
          text-decoration: none;
        }

        .app-store-badge {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 16px;
          background: #1a1a1a;
          border: 1px solid #333;
          border-radius: 8px;
          transition: background-color 0.2s;
        }

        .app-store-badge:hover {
          background: #2a2a2a;
        }

        .app-icon {
          font-size: 24px;
        }

        .app-text {
          display: flex;
          flex-direction: column;
        }

        .app-subtitle {
          font-size: 10px;
          color: #999;
        }

        .app-title {
          font-size: 14px;
          font-weight: 600;
          color: #fff;
        }

        .app-disclaimer {
          font-size: 11px;
          color: #757575;
          font-style: italic;
          line-height: 1.4;
          margin-top: 8px;
          max-width: 280px;
        }

        .footer-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 32px;
          border-top: 1px solid rgba(238, 238, 238, 0.3);
        }

        @media (max-width: 768px) {
          .footer-bottom {
            flex-direction: column;
            gap: 20px;
            text-align: center;
          }
        }

        .paypal-info {
          color: #757575;
          font-size: 14px;
        }

        .honey-paypal {
          font-weight: 500;
          margin-bottom: 4px;
          display: block;
        }

        .paypal-text {
          margin: 0;
        }

        .learn-more-link {
          color: #757575;
          text-decoration: underline;
        }

        .learn-more-link:hover {
          color: #9e9e9e;
        }

        .country-selector {
          display: flex;
          align-items: center;
          gap: 12px;
          color: #fff;
          font-size: 14px;
        }

        .country-text {
          padding-right: 12px;
          border-right: 1px solid #e0e0e0;
        }

        .language-text {
          padding-left: 12px;
        }
      `}</style>
    </footer>
  )
}

export default Footer
